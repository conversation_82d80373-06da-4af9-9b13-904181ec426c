# VS Code Extension Directory Management - Multiple Locations

## 🎯 Your Question: Can I Set Both?

**Short Answer**: VS Code only looks in **one** extensions directory at a time, determined by the `USERPROFILE` environment variable.

**Long Answer**: You have several options to manage this mixed environment scenario.

## 🔧 Current Environment Analysis

```cmd
USERPROFILE: C:\JUPYTERWORK
USERNAME: KristianTaylor-<PERSON>øren
HOMEPATH: \Users\KristianTaylor-<PERSON>øren
```

### VS Code Extension Search Logic

VS Code looks for extensions in: `%USERPROFILE%\.vscode\extensions\`

- **With current USERPROFILE**: `C:\JUPYTERWORK\.vscode\extensions\`
- **With default USERPROFILE**: `C:\Users\<USER>\.vscode\extensions\`

## 📋 Available Solutions

### Option 1: Symbolic Link (Recommended)

Create a symbolic link to make both locations work:

```cmd
# Create symbolic link from JUPYTERWORK to default location
mklink /J "C:\JUPYTERWORK\.vscode\extensions" "C:\Users\<USER>\.vscode\extensions"
```

**Pros**:

- Extensions available in both locations
- Automatic synchronization
- No need to move extensions manually

**Cons**:

- Requires admin privileges
- More complex to maintain

### Option 2: Environment Variable Management

Switch USERPROFILE temporarily for VS Code:

```cmd
# Create batch file to launch VS Code with default USERPROFILE
@echo off
set USERPROFILE=C:\Users\<USER>\Users\KristianTaylor-Søren\AppData\Local\Programs\Microsoft VS Code\Code.exe"
```

**Pros**:

- Clean separation of environments
- Uses default extension location
- No file system modifications

**Cons**:

- Need to launch VS Code differently
- Settings might be in different locations

### Option 3: Extension Synchronization Script

Create a script to sync extensions between locations:

```cmd
# PowerShell script to sync extensions
robocopy "C:\Users\<USER>\.vscode\extensions" "C:\JUPYTERWORK\.vscode\extensions" /MIR /XD .obsolete
```

**Pros**:

- Keep both locations updated
- Full control over synchronization
- Can be automated

**Cons**:

- Manual process
- Risk of conflicts
- More maintenance

### Option 4: Portable VS Code

Use VS Code Portable version:

```cmd
# Download VS Code Portable
# Place in: C:\VSCodePortable\
# Extensions will be in: C:\VSCodePortable\data\extensions\
```

**Pros**:

- Independent of system environment
- Fully portable
- No environment conflicts

**Cons**:

- Separate VS Code installation
- Different update mechanism

## 🚀 Recommended Implementation

### Step 1: Test Current Setup

First, verify the current solution works:

```cmd
# Check current extension location
dir "C:\JUPYTERWORK\.vscode\extensions\quarto.quarto-1.123.0"

# Test VS Code detection
code --list-extensions
```

### Step 2: Create Backup

Before making changes:

```cmd
# Backup current JUPYTERWORK extensions
xcopy "C:\JUPYTERWORK\.vscode\extensions" "C:\JUPYTERWORK\.vscode\extensions_backup" /E /I /Y
```

### Step 3: Implement Symbolic Link (Recommended)

```cmd
# Run as Administrator
# Remove current JUPYTERWORK extensions directory
rmdir "C:\JUPYTERWORK\.vscode\extensions" /s /q

# Create symbolic link
mklink /J "C:\JUPYTERWORK\.vscode\extensions" "C:\Users\<USER>\.vscode\extensions"

# Move Quarto extension back to default location
move "C:\JUPYTERWORK\.vscode\extensions_backup\quarto.quarto-1.123.0" "C:\Users\<USER>\.vscode\extensions\"
```

### Step 4: Verify Setup

```cmd
# Check symbolic link
dir "C:\JUPYTERWORK\.vscode\extensions"

# Should show the same content as:
dir "C:\Users\<USER>\.vscode\extensions"

# Test VS Code
code --list-extensions
```

## 🔍 Key Considerations

### User Settings Location

With different USERPROFILE values, settings are stored in different locations:

- **JUPYTERWORK**: `C:\JUPYTERWORK\AppData\Roaming\Code\User\settings.json`
- **Default**: `C:\Users\<USER>\AppData\Roaming\Code\User\settings.json`

### Extension Settings

Some extensions store settings in:

- `%USERPROFILE%\.vscode\extensions\<extension>\`
- `%APPDATA%\Code\User\globalStorage\`

### Workspace Settings

These are unaffected by USERPROFILE:

- `C:\AnacondaProjects\04_PREP\.vscode\settings.json`

## 📊 Summary Table

| Solution               | Complexity | Maintenance | Compatibility | Recommended |
| ---------------------- | ---------- | ----------- | ------------- | ----------- |
| **Symbolic Link**      | Medium     | Low         | High          | ✅ **YES**  |
| **Environment Switch** | Low        | Medium      | High          | ⚠️ Maybe    |
| **Sync Script**        | High       | High        | Medium        | ❌ No       |
| **Portable VS Code**   | Medium     | Medium      | Medium        | ⚠️ Maybe    |

## 🎯 Final Recommendation

**Use Symbolic Link (Option 1)** because:

1. Extensions work in both environments
2. No need to manually manage locations
3. Automatic synchronization
4. Maintains current workflow
5. VS Code works regardless of USERPROFILE value

## 🚀 IMPLEMENTATION READY

I've created two automated setup scripts for you:

### Option A: Batch Script (Windows CMD)

```cmd
# Run as Administrator
.\setup_dual_extensions.bat
```

### Option B: PowerShell Script (Recommended)

```powershell
# Run PowerShell as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\setup_dual_extensions.ps1
```

Both scripts will:

1. ✅ Backup your current extensions
2. ✅ Create symbolic link from JUPYTERWORK to default location
3. ✅ Consolidate all extensions in one location
4. ✅ Test the setup
5. ✅ Provide next steps

## 📋 Quick Start Instructions

1. **Right-click on PowerShell** → **Run as Administrator**
2. **Navigate to the project folder**:

   ```powershell
   cd "C:\AnacondaProjects\04_PREP"
   ```

3. **Run the setup script**:

   ```powershell
   .\setup_dual_extensions.ps1
   ```

4. **Follow the prompts** and restart VS Code when complete

## 🔧 Manual Steps (if scripts fail)

If the automated scripts don't work, you can run these commands manually as Administrator:

```cmd
# Backup current extensions
xcopy "C:\JUPYTERWORK\.vscode\extensions" "C:\JUPYTERWORK\.vscode\extensions_backup" /E /I /Y

# Remove JUPYTERWORK extensions directory
rmdir "C:\JUPYTERWORK\.vscode\extensions" /s /q

# Create symbolic link
mklink /J "C:\JUPYTERWORK\.vscode\extensions" "C:\Users\<USER>\.vscode\extensions"

# Restore extensions to default location
xcopy "C:\JUPYTERWORK\.vscode\extensions_backup\*" "C:\Users\<USER>\.vscode\extensions\" /E /I /Y
```

## ✅ Expected Results

After running the setup:

- Both `C:\JUPYTERWORK\.vscode\extensions\` and `C:\Users\<USER>\.vscode\extensions\` will show the same content
- Extensions installed in either location will be available in both
- VS Code will work regardless of your USERPROFILE setting
- Quarto extension will be properly detected and functional

**Ready to proceed with the setup?**
