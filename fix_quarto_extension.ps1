# PowerShell script to fix Quarto VS Code extension issues
# Run this script as Administrator if the basic fix doesn't work

Write-Host "=== Quarto VS Code Extension Fix Script ===" -ForegroundColor Green
Write-Host ""

# Check current Python path
Write-Host "Current Python path:" -ForegroundColor Yellow
$pythonPath = (Get-Command python -ErrorAction SilentlyContinue).Source
Write-Host $pythonPath

# Check if we're in the right conda environment
Write-Host ""
Write-Host "Checking conda environment..." -ForegroundColor Yellow
$condaInfo = python -c "import sys; print(sys.prefix)"
Write-Host "Python environment: $condaInfo"

# Set QUARTO_PYTHON environment variable
$quartoPython = "C:\AnacondaPath\envs\prep_book\python.exe"
Write-Host ""
Write-Host "Setting QUARTO_PYTHON environment variable..." -ForegroundColor Yellow
Write-Host "Target path: $quartoPython"

try {
    # Set for current session
    $env:QUARTO_PYTHON = $quartoPython
    Write-Host "✓ Set for current session" -ForegroundColor Green

    # Set permanently (requires admin rights)
    [System.Environment]::SetEnvironmentVariable("QUARTO_PYTHON", $quartoPython, "User")
    Write-Host "✓ Set permanently for user" -ForegroundColor Green
} catch {
    Write-Host "× Failed to set environment variable: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Try running as Administrator" -ForegroundColor Yellow
}

# Check if Quarto can find the right Python
Write-Host ""
Write-Host "Testing Quarto Python detection..." -ForegroundColor Yellow
try {
    $quartoCheck = quarto check --quiet 2>&1
    Write-Host "✓ Quarto check completed" -ForegroundColor Green
} catch {
    Write-Host "× Quarto check failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Green
Write-Host "1. Restart VS Code completely"
Write-Host "2. Open your workspace"
Write-Host "3. Try Ctrl+Shift+P and search for 'Quarto'"
Write-Host "4. Commands should now work without 'not found' errors"
Write-Host ""
Write-Host "If issues persist, check the troubleshooting guide in QUARTO_TROUBLESHOOTING.md"
