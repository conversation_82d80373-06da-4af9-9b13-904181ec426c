import matplotlib.pyplot as plt
import numpy as np

# Set a seed for reproducibility
np.random.seed(42)

# Generate some data
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)
y3 = np.sin(x) * np.cos(x)

# Create a figure with multiple subplots
plt.figure(figsize=(12, 8))

# First subplot
plt.subplot(2, 2, 1)
plt.plot(x, y1, 'b-', linewidth=2)
plt.title('Sine Wave')
plt.xlabel('x')
plt.ylabel('sin(x)')
plt.grid(True)

# Second subplot
plt.subplot(2, 2, 2)
plt.plot(x, y2, 'r-', linewidth=2)
plt.title('Cosine Wave')
plt.xlabel('x')
plt.ylabel('cos(x)')
plt.grid(True)

# Third subplot
plt.subplot(2, 2, 3)
plt.plot(x, y3, 'g-', linewidth=2)
plt.title('Sine * Cosine')
plt.xlabel('x')
plt.ylabel('sin(x) * cos(x)')
plt.grid(True)

# Fourth subplot - scatter plot
plt.subplot(2, 2, 4)
plt.scatter(np.random.rand(50), np.random.rand(50), 
            c=np.random.rand(50), s=np.random.rand(50) * 500, 
            alpha=0.7, cmap='viridis')
plt.title('Random Scatter Plot')
plt.xlabel('x')
plt.ylabel('y')
plt.grid(True)

# Adjust layout
plt.tight_layout()

# Save the figure
plt.savefig('../Figures/sample_figure.png', dpi=300)
plt.savefig('../Figures/sample_figure.pdf')

print("Figure saved to Figures/sample_figure.png and Figures/sample_figure.pdf")
