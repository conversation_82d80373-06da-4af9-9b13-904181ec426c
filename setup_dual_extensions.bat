@echo off
echo ====================================================
echo VS Code Extensions Dual Setup - Symbolic Link Solution
echo ====================================================
echo.
echo This script will:
echo 1. Backup current JUPYTERWORK extensions
echo 2. Create symbolic link from JUPYTERWORK to default location
echo 3. Consolidate all extensions in one location
echo 4. Test the setup
echo.
echo IMPORTANT: This script requires Administrator privileges
echo.
pause

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script requires Administrator privileges
    echo Please run as Administrator
    pause
    exit /b 1
)

echo [1/5] Checking current extension locations...
echo.
echo JUPYTERWORK extensions:
dir "C:\JUPYTERWORK\.vscode\extensions\" 2>nul
if %errorLevel% neq 0 echo No extensions found in JUPYTERWORK location

echo.
echo Default location extensions:
dir "C:\Users\<USER>\.vscode\extensions\" 2>nul
if %errorLevel% neq 0 echo No extensions found in default location

echo.
echo [2/5] Creating backup of JUPYTERWORK extensions...
if exist "C:\JUPYTERWORK\.vscode\extensions\" (
    xcopy "C:\JUPYTERWORK\.vscode\extensions" "C:\JUPYTERWORK\.vscode\extensions_backup" /E /I /Y
    if %errorLevel% equ 0 (
        echo ✅ Backup created successfully
    ) else (
        echo ❌ Backup failed
        pause
        exit /b 1
    )
) else (
    echo ℹ️  No JUPYTERWORK extensions directory to backup
)

echo.
echo [3/5] Removing current JUPYTERWORK extensions directory...
if exist "C:\JUPYTERWORK\.vscode\extensions\" (
    rmdir "C:\JUPYTERWORK\.vscode\extensions" /s /q
    if %errorLevel% equ 0 (
        echo ✅ Directory removed successfully
    ) else (
        echo ❌ Failed to remove directory
        pause
        exit /b 1
    )
)

echo.
echo [4/5] Creating symbolic link...
REM Ensure default extensions directory exists
if not exist "C:\Users\<USER>\.vscode\extensions\" (
    mkdir "C:\Users\<USER>\.vscode\extensions"
    echo ✅ Created default extensions directory
)

REM Create the symbolic link
mklink /J "C:\JUPYTERWORK\.vscode\extensions" "C:\Users\<USER>\.vscode\extensions"
if %errorLevel% equ 0 (
    echo ✅ Symbolic link created successfully
) else (
    echo ❌ Failed to create symbolic link
    echo Restoring backup...
    if exist "C:\JUPYTERWORK\.vscode\extensions_backup\" (
        xcopy "C:\JUPYTERWORK\.vscode\extensions_backup" "C:\JUPYTERWORK\.vscode\extensions" /E /I /Y
    )
    pause
    exit /b 1
)

echo.
echo [5/5] Restoring extensions from backup...
if exist "C:\JUPYTERWORK\.vscode\extensions_backup\" (
    echo Copying extensions from backup to default location...
    xcopy "C:\JUPYTERWORK\.vscode\extensions_backup\*" "C:\Users\<USER>\.vscode\extensions\" /E /I /Y
    if %errorLevel% equ 0 (
        echo ✅ Extensions restored successfully
    ) else (
        echo ⚠️  Some extensions may not have been restored
    )
)

echo.
echo ====================================================
echo SETUP COMPLETE!
echo ====================================================
echo.
echo Testing the setup...
echo.
echo Extensions in JUPYTERWORK location:
dir "C:\JUPYTERWORK\.vscode\extensions\" | findstr /v "Directory of"
echo.
echo Extensions in default location:
dir "C:\Users\<USER>\.vscode\extensions\" | findstr /v "Directory of"
echo.
echo Both locations should show the same content (symbolic link working)
echo.
echo Next steps:
echo 1. Restart VS Code completely
echo 2. Test: Open test_extension_final.qmd
echo 3. Verify: Press Ctrl+Shift+P and look for "Quarto: Verify Installation"
echo 4. Check: Output panel (View → Output → Quarto)
echo.
echo If everything works, you can delete the backup:
echo rmdir "C:\JUPYTERWORK\.vscode\extensions_backup" /s /q
echo.
pause
