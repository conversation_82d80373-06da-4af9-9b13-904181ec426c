@echo off
echo ======================================
echo QUARTO EXTENSION DIAGNOSTIC SCRIPT
echo Based on GitHub Issue #2143
echo ======================================
echo.

echo 1. Testing Quarto CLI access...
echo Command Prompt:
"C:\Quarto\quarto_1_7_32\bin\quarto.exe" --version
echo.

echo PowerShell:
powershell -c "C:\Quarto\quarto_1_7_32\bin\quarto.exe --version"
echo.

echo 2. Checking VS Code extension directory...
dir "C:\Users\<USER>\.vscode\extensions\quarto.quarto-1.123.0" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Extension found in correct location
    dir "C:\Users\<USER>\.vscode\extensions\quarto.quarto-1.123.0\out\main.js" >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ Main extension file exists
    ) else (
        echo ❌ Main extension file missing
    )
) else (
    echo ❌ Extension not found in expected location
)
echo.

echo 3. Environment check...
echo USERPROFILE: %USERPROFILE%
echo APPDATA: %APPDATA%
echo.

echo 4. Next steps:
echo - Open VS Code
echo - Open a .qmd file
echo - Press Ctrl+Shift+P
echo - Type "Developer: Reload Window"
echo - Try "Quarto: Verify Installation"
echo - Check Output panel for Quarto logs
echo.

pause
