---
title: 'Sea Level Rise Analysis for Vejlerne'
subtitle: 'Speed-Optimized QGIS Workflow with Best Practices'
author: 'Sea Level Rise Analysis Team'
date: today
format:
  html:
    theme: cosmo
    toc: true
    toc-depth: 3
    number-sections: true
    code-fold: false
    embed-resources: true
    page-layout: article
    grid:
      sidebar-width: 250px
      body-width: 900px
      margin-width: 300px
execute:
  echo: false
  warning: false
  message: false
---

::: {.callout-note icon="info-circle"}

## Quick Start Information

- **Target Time:** 1.5-2 hours total
- **QGIS Version:** 3.40.7 'Bratislava'
- **Coordinate System:** EPSG:25832 (ETRS89 / UTM zone 32N)
- **Output:** Single scenario flood extent for ScalgoLIVE comparison
- **Quality Focus:** Speed with essential accuracy controls

:::

## Version Context & Requirements

::: {.callout-important icon="exclamation-triangle"}

**Required QGIS Components:**

- QGIS 3.40.7 with GDAL and SAGA processing providers
- Processing Toolbox enabled (Processing → Toolbox)
- Danish projection system support (EPSG:25832)

:::

### Initial Configuration

1. **Settings → Options → Processing:**

   - **General Tab:**
     - Invalid features filtering: `Skip features with invalid geometries` ✅
     - Keep dialog open after running: ✅
   - **Providers → SAGA:**
     - Enable SAGA provider ✅

2. **Project → Properties → CRS:**
   - Set to: `EPSG:25832 - ETRS89 / UTM zone 32N`

::: {.callout-warning icon="shield-exclamation"}

**DATA SAFETY:** Always work with copies of original data. Create project folder:

```yaml
Vejlerne_SLR_Analysis/
├── raw_data/        (original downloads)
├── processing/      (working files)
└── outputs/         (final results)
```

:::

## Phase 1: Data Collection & Verification {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 20 minutes

:::

### Step 1: Download Essential Data

1. **Navigate to Dataforsyningen.dk:**

   - Dataset: `DHM/Terræn` (DTM) - **NOT DHM/Overflade**
   - Resolution: 0.4m or 1.6m
   - Format: GeoTIFF
   - Coverage: Vejlerne area + 500m buffer

2. **Optional coastline reference:**
   - Dataset: `Kystlinje` (Coastline)
   - Format: GeoPackage

::: {.callout-important icon="exclamation-diamond"}

**Critical:** Verify vertical datum = **DVR90** in metadata before proceeding

:::

### Step 2: Load and Inspect Data

1. **Drag DTM file** directly into QGIS
2. **Check CRS:** Should auto-detect EPSG:25832
3. **Quick validation:**
   - Right-click layer → Properties → Information
   - Verify elevation range (typically -5 to +50m for coastal areas)
   - Note any NoData values

## Phase 2: DEM Preprocessing {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 15 minutes

:::

### Step 3: Fill Sinks (Critical for Coastal Analysis)

::: {.callout-warning icon="water"}

**Why this matters:** Unfilled sinks create artificial ponding that disconnects flood areas from the coast, producing false inland flooding.

:::

1. **Processing Toolbox → SAGA → Terrain Analysis - Hydrology → Fill Sinks (Wang & Liu):**

   - **DEM:** Your DTM layer
   - **Minimum Slope `[Degree]`:** `0.01`
   - **Filled DEM:** Save as `DTM_Filled.tif`
   - Run algorithm

2. **Quick check:**
   - Compare original vs filled DEM
   - Differences should be minimal in coastal areas

### Step 4: Define Flood Scenario

::: {.callout-note icon="chart-line"}

**2100 Sea Level Rise Scenario:**

- Current Mean High Water: **+1.0m** DVR90
- Projected SLR by 2100: **+0.8m** (moderate scenario)
- **Total flood threshold: +1.8m** DVR90

:::

## Phase 3: Flood Extent Calculation {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 20 minutes

:::

### Step 5: Calculate Flood Extent

1. **Raster → Raster Calculator:**

   - **Expression:** `"DTM_Filled@1" <= 1.8`
   - **Output layer:** `flood_extent_2100.tif`
   - **Output format:** GeoTIFF
   - **Add result to project:** ✅

2. **Style for visualization:**
   - Right-click → Symbology
   - Render type: `Paletted/Unique values`
   - Color: Blue for value 1 (flooded)
   - Transparent for value 0

### Step 6: Initial Quality Check

::: {.columns}

::: {.column width="50%"}

**Expected patterns:**

- ✅ Flooding along coastline
- ✅ Low-lying meadows affected
- ✅ Higher ground remains dry

:::

::: {.column width="50%"}

**Warning signs:**

- ❌ Isolated inland pools
- ❌ Jagged coastal edges
- ❌ Excessive inland penetration

:::

:::

## Phase 4: Vectorization & Connectivity {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 25 minutes

:::

### Step 7: Convert to Polygons

1. **Raster → Conversion → Polygonize:**
   - **Input layer:** `flood_extent_2100.tif`
   - **Band number:** Band 1
   - **Field name:** `flooded`
   - **Use 8-connectivity:** ✅
   - **Output:** `flood_polygons_raw.gpkg`

### Step 8: Coastal Connectivity Filter

::: {.callout-important icon="link"}

**Critical step:** Remove disconnected inland flooding caused by DEM artifacts

:::

1. **Create coastal buffer (if no coastline layer):**

   - Select flooded polygons touching image edge
   - Vector → Geoprocessing → Buffer
   - Distance: 10 meters
   - Save as `coastal_zone.gpkg`

2. **Select connected flooding:**

   - Vector → Research Tools → Select by Location
   - Select features from: `flood_polygons_raw`
   - That: `intersect`
   - By comparing to: `coastal_zone`
   - Click Run

3. **Export selection:**
   - Right-click → Export → Save Selected Features As
   - Save as: `flood_connected.gpkg`

### Step 9: Clean and Simplify

1. **Remove small artifacts:**

   - Select by Expression: `$area < 1000`
   - Delete selected features

2. **Dissolve boundaries:**
   - Vector → Geoprocessing → Dissolve
   - Input: `flood_connected`
   - Dissolve all
   - Output: `flood_extent_final.gpkg`

## Phase 5: Analysis & Export {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 15 minutes

:::

### Step 10: Calculate Metrics

1. **Add area field:**

   - Open Attribute Table
   - Field Calculator → Create new field
   - Field name: `area_ha`
   - Type: Decimal number
   - Expression: `round($area / 10000, 2)`

2. **Document key statistics:**
   - Total flooded area (hectares)
   - Number of distinct flood zones
   - Maximum inland penetration

### Step 11: Final Export

1. **Export shapefile for comparison:**

   - Right-click → Export → Save Features As
   - Format: ESRI Shapefile
   - File name: `Vejlerne_SLR_2100_[DATE].shp`
   - CRS: EPSG:25832

2. **Create metadata file:**

   ```yaml
   Flood Scenario: 2100 SLR (+0.8m)
   Base elevation: +1.0m DVR90
   Total threshold: +1.8m DVR90
   DEM accuracy: ±0.05-0.10m
   Processing date: [DATE]
   Sink filling: Yes (0.01° minimum slope)
   Connectivity: Coastal-connected only
   ```

## Quality Assurance Checklist

::: {.panel-tabset group="qa"}

### Quick Visual Checks

| Validation Point         | Expected Result                | Pass |
| :----------------------- | :----------------------------- | :--: |
| Flood connects to coast  | Continuous from sea            |  ☐   |
| No isolated inland pools | All flooding coastal-connected |  ☐   |
| Realistic penetration    | Follows topography             |  ☐   |
| Clean boundaries         | No pixelated edges             |  ☐   |

### Quantitative Checks

| Metric           | Reasonable Range | Your Result |
| :--------------- | :--------------- | :---------- |
| Total area       | 100-5000 ha      | **\_** ha   |
| Mean penetration | 0.5-5 km         | **\_** km   |
| Elevation check  | All ≤ 1.8m       | ☐ Yes       |

### Uncertainty Sources

::: {.callout-note icon="info-circle"}

**Document these limitations:**

- DEM vertical accuracy: ±0.05-0.10m
- Horizontal accuracy: ±0.5-1.0m
- Datum uncertainty: ±0.02m
- SLR projection uncertainty: ±0.2-0.4m
- **Combined uncertainty: ±0.3-0.5m**

:::

:::

## Time-Saving Tips

::: {.columns}

::: {.column width="50%"}

**Do this:**

- ✅ Work in memory when possible
- ✅ Use keyboard shortcuts
- ✅ Save project file frequently
- ✅ Process smallest area needed

:::

::: {.column width="50%"}

**Skip this:**

- ❌ Multiple scenario iterations
- ❌ Detailed hydrological modeling
- ❌ Infrastructure impact analysis
- ❌ Publication-quality cartography

:::

:::

## Expected Deliverables

::: {.callout-success icon="check-circle"}

**Final outputs for ScalgoLIVE comparison:**

1. **Shapefile:** `Vejlerne_SLR_2100_[DATE].shp`

   - Single-part polygon
   - Area calculation included
   - Coastal-connected flooding only

2. **Summary statistics:**

   - Total flooded area (hectares)
   - Comparison-ready metrics

3. **Documentation:**
   - Processing assumptions
   - Uncertainty statement
   - Method summary

:::

## Optional Extensions

::: {.callout-tip icon="plus-circle" collapse="true"}

## If Extra Time Available (+30 min)

### Add 2050 Scenario

1. Repeat flood calculation with +1.3m threshold
2. Create difference map: 2100 minus 2050
3. Calculate incremental flood area

### Enhanced Validation

1. Create elevation profile across flood boundary
2. Generate flood depth raster (flood elevation - DEM)
3. Compare with historical flood events

### Presentation Materials

1. Create styled map with basemap
2. Add scale bar and north arrow
3. Export as GeoPDF for sharing

:::

::: {.callout-warning icon="exclamation-triangle"}

**Remember:** This workflow prioritizes **speed and comparability** over comprehensive analysis. For planning decisions, conduct full hydrological modeling with multiple scenarios and uncertainty analysis.

:::
