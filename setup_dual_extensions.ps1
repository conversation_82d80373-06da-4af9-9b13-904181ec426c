# VS Code Extensions Dual Setup - PowerShell Version
# This version attempts to use PowerShell's New-Item for symbolic links

Write-Host "=====================================================" -ForegroundColor Green
Write-Host "VS Code Extensions Dual Setup - PowerShell Version" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green
Write-Host ""

# Function to test if running as admin
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Check admin privileges
if (-not (Test-Administrator)) {
    Write-Host "WARNING: Not running as Administrator" -ForegroundColor Yellow
    Write-Host "Symbolic links might not work without admin privileges" -ForegroundColor Yellow
    Write-Host "Consider running PowerShell as Administrator" -ForegroundColor Yellow
    Write-Host ""
    $continue = Read-Host "Do you want to continue anyway? (y/n)"
    if ($continue -ne "y") {
        exit
    }
}

Write-Host "[1/5] Checking current extension locations..." -ForegroundColor Cyan
Write-Host ""

$jupyterworkPath = "C:\JUPYTERWORK\.vscode\extensions"
$defaultPath = "C:\Users\<USER>\.vscode\extensions"
$backupPath = "C:\JUPYTERWORK\.vscode\extensions_backup"

Write-Host "JUPYTERWORK extensions:" -ForegroundColor White
if (Test-Path $jupyterworkPath) {
    Get-ChildItem $jupyterworkPath | Format-Table Name, LastWriteTime -AutoSize
} else {
    Write-Host "No extensions found in JUPYTERWORK location" -ForegroundColor Gray
}

Write-Host "Default location extensions:" -ForegroundColor White
if (Test-Path $defaultPath) {
    Get-ChildItem $defaultPath | Format-Table Name, LastWriteTime -AutoSize
} else {
    Write-Host "No extensions found in default location" -ForegroundColor Gray
}

Write-Host "[2/5] Creating backup..." -ForegroundColor Cyan
if (Test-Path $jupyterworkPath) {
    try {
        Copy-Item $jupyterworkPath $backupPath -Recurse -Force
        Write-Host "✅ Backup created successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Backup failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "ℹ️  No JUPYTERWORK extensions directory to backup" -ForegroundColor Yellow
}

Write-Host "[3/5] Removing current JUPYTERWORK extensions directory..." -ForegroundColor Cyan
if (Test-Path $jupyterworkPath) {
    try {
        Remove-Item $jupyterworkPath -Recurse -Force
        Write-Host "✅ Directory removed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to remove directory: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host "[4/5] Creating symbolic link..." -ForegroundColor Cyan
# Ensure default extensions directory exists
if (-not (Test-Path $defaultPath)) {
    New-Item -ItemType Directory -Path $defaultPath -Force | Out-Null
    Write-Host "✅ Created default extensions directory" -ForegroundColor Green
}

# Create symbolic link using PowerShell
try {
    New-Item -ItemType SymbolicLink -Path $jupyterworkPath -Target $defaultPath -Force
    Write-Host "✅ Symbolic link created successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create symbolic link: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Restoring backup..." -ForegroundColor Yellow
    if (Test-Path $backupPath) {
        Copy-Item $backupPath $jupyterworkPath -Recurse -Force
    }
    exit 1
}

Write-Host "[5/5] Restoring extensions from backup..." -ForegroundColor Cyan
if (Test-Path $backupPath) {
    try {
        $backupItems = Get-ChildItem $backupPath
        foreach ($item in $backupItems) {
            $targetPath = Join-Path $defaultPath $item.Name
            if (-not (Test-Path $targetPath)) {
                Copy-Item $item.FullName $targetPath -Recurse -Force
            }
        }
        Write-Host "✅ Extensions restored successfully" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Some extensions may not have been restored: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=====================================================" -ForegroundColor Green
Write-Host "SETUP COMPLETE!" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green
Write-Host ""

Write-Host "Testing the setup..." -ForegroundColor Cyan
Write-Host ""
Write-Host "Extensions in JUPYTERWORK location:" -ForegroundColor White
Get-ChildItem $jupyterworkPath | Format-Table Name, LastWriteTime -AutoSize

Write-Host "Extensions in default location:" -ForegroundColor White
Get-ChildItem $defaultPath | Format-Table Name, LastWriteTime -AutoSize

Write-Host "Both locations should show the same content (symbolic link working)" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart VS Code completely" -ForegroundColor White
Write-Host "2. Test: Open test_extension_final.qmd" -ForegroundColor White
Write-Host "3. Verify: Press Ctrl+Shift+P and look for 'Quarto: Verify Installation'" -ForegroundColor White
Write-Host "4. Check: Output panel (View → Output → Quarto)" -ForegroundColor White
Write-Host ""
Write-Host "If everything works, you can delete the backup:" -ForegroundColor Gray
Write-Host "Remove-Item '$backupPath' -Recurse -Force" -ForegroundColor Gray
Write-Host ""
Read-Host "Press Enter to exit"
