---
title: 'Simplified Sea Level Rise Analysis'
subtitle: '1-Day Vejlerne Workflow'
author: 'Sea Level Rise Analysis Team'
date: today
format:
  html:
    theme: cosmo
    toc: true
    toc-depth: 3
    number-sections: true
    code-fold: false
    embed-resources: true
    page-layout: article
    grid:
      sidebar-width: 250px
      body-width: 900px
      margin-width: 300px
execute:
  echo: false
  warning: false
  message: false
---

::: {.callout-note icon="info-circle"}

## Quick Start Info

- **Target Time:** 1.5-2 hours total
- **QGIS Version:** 3.28+
- **Coordinate System:** EPSG:25832 (Danish standard)
- **Output:** Single scenario flood extent for comparative analysis

:::

## Essential Setup

::: {.callout-warning icon="exclamation-triangle"}

**Create project backup folder** - Save original DEM before processing.

:::

## Phase 1: Essential Data Only {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 20 minutes

:::

### Step 1: Minimum Data Download

1. **Dataforsyningen.dk → Download:**

   - **DHM/Terrain** (1m DEM) - **Priority download only**
   - **Coastline** layer (optional for visual reference)

2. **Quick QGIS Setup:**
   - **New project** → Drag DEM file directly
   - **CRS automatically set** to EPSG:25832
   - **Skip** other datasets for speed

## Phase 2: Single Scenario Setup {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 10 minutes

:::

### Step 2: Choose One Representative Scenario

::: {.callout-important icon="exclamation-diamond"}

**Use 2100 projection for maximum impact assessment:**

- **Current Mean High Water:** +1.0m DVR90
- **Sea Level Rise by 2100:** +0.8m
- **Total flood threshold:** +1.8m DVR90

:::

### Step 3: Quick DEM Inspection

1. **Style DEM** with simple elevation ramp
2. **Visual check:** Verify elevation range looks reasonable
3. **Skip** detailed quality control for speed

## Phase 3: Direct Flood Calculation {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 25 minutes

:::

### Step 4: Single Raster Calculator Operation

1. **Raster → Raster Calculator**
2. **Simple expression:** `"DHM_layer@1" <= 1.8`
3. **Output settings:**
   - **Filename:** `vejlerne_slr_flood.tif`
   - **Format:** GeoTIFF
   - **Add to project:** ✅ Checked

::: {.callout-note icon="lightbulb"}

**Result:** Binary raster (1 = flooded, 0 = not flooded)

:::

### Step 5: Immediate Visual Check

1. **Style flood raster:** Blue for flooded areas
2. **Quick assessment:**
   - ✅ Coastal areas show flooding
   - ✅ Inland areas mostly unaffected
   - ✅ No obvious data errors

## Phase 4: Fast Vectorization {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 20 minutes

:::

### Step 6: Direct Polygon Conversion

1. **Raster → Conversion → Polygonize**
   - **Input:** `vejlerne_slr_flood.tif`
   - **Field name:** "flooded"
   - **8-connectivity:** ✅ Checked
   - **Output:** `flood_polygons.shp`

### Step 7: Essential Cleanup Only

1. **Open Attribute Table**

   - **Select** where flooded = 0
   - **Delete** non-flooded polygons

2. **Quick area filter:**

   - **Vector → Research Tools → Select by Expression**
   - **Expression:** `$area < 1000`
   - **Delete** small artifacts (< 1000 m²)

3. **Simple dissolve:**
   - **Vector → Geoprocessing → Dissolve**
   - **Dissolve field:** flooded

## Phase 5: Final Export {.unnumbered}

::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 15 minutes

:::

### Step 8: Calculate Key Metrics

1. **Field Calculator** → Add field: "area_ha"
2. **Expression:** `$area / 10000`
3. **Sum total flooded area** in attribute table

### Step 9: Export Results

1. **Right-click layer → Export → Save Features As**

   - **Format:** ESRI Shapefile
   - **Filename:** `vejlerne_slr_2100_simple.shp`
   - **CRS:** EPSG:25832

2. **Create simple map** for documentation:
   - **Print Layout** → Add map + legend
   - **Export as PDF** for quick reference

## Maximum Efficiency Shortcuts

::: {.panel-tabset group="shortcuts"}

## Skip These Steps

::: {.columns}

::: {.column width="50%"}

- ❌ Multiple scenario modeling
- ❌ Detailed DEM quality control
- ❌ Advanced polygon cleanup
- ❌ Connectivity analysis

:::

::: {.column width="50%"}

- ❌ Hillshade creation
- ❌ Complex styling

:::

:::

## Focus Only On

::: {.columns}

::: {.column width="50%"}

- ✅ **Single 2100 SLR scenario** (worst-case)
- ✅ **Direct elevation threshold** (1.8m DVR90)

:::

::: {.column width="50%"}

- ✅ **Basic polygon cleanup** (remove small artifacts)
- ✅ **Total area calculation** (for comparison)

:::

:::

## One-Click Shortcuts

- **Use default settings** for all tools
- **Skip intermediate file saves** (work in memory)
- **Apply simple symbology** (blue = flooded)
- **Export final result only**

:::

## Quality Control Checklist

::: {.callout-tip icon="check-circle" collapse="false"}

**Estimated Time:** 5 minutes

:::

::: {.panel-tabset group="validation"}

### Visual Validation

| Check                                   | Status |
| :-------------------------------------- | :----: |
| Flooded areas connect to coast          |   ✅   |
| No isolated inland flooding             |   ✅   |
| Reasonable total area (visual estimate) |   ✅   |
| Clean polygon boundaries                |   ✅   |

### Metric Validation

| Check                                     | Status |
| :---------------------------------------- | :----: |
| Total flooded area calculated in hectares |   ✅   |
| File exports successfully                 |   ✅   |
| CRS matches project (EPSG:25832)          |   ✅   |

:::

## Expected Results

::: {.callout-note icon="file-earmark-check"}

## Deliverables

- **Single shapefile:** Sea level rise flood extent (2100 scenario)
- **Area metric:** Total hectares affected
- **Processing time:** < 2 hours
- **Quality level:** Suitable for comparative validation

:::

### Comparison Metrics

- **Total flooded area** (hectares)
- **Approximate coastal penetration** (visual estimate)
- **Major low-lying areas** identified

## Optional Extensions

::: {.callout-tip icon="plus-circle" collapse="true"}

## If Time Permits (+30 minutes)

1. **Add 2050 scenario** using 1.3m threshold
2. **Create difference map** between scenarios
3. **Export both results** for comparison

:::

::: {.callout-note icon="info-circle"}

This ultra-simplified workflow prioritizes **speed and basic accuracy** - perfect for quick comparative validation while minimizing time investment.

:::
