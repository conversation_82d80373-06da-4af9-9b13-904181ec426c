@echo off
echo Testing Quarto Extension...
echo.

echo 1. Verifying VS Code is installed:
code --version
echo.

echo 2. Verifying Quarto extension is installed:
code --list-extensions | findstr quarto
echo.

echo 3. Verifying Quarto CLI is working:
"C:\Quarto\quarto_1_7_32\bin\quarto.exe" --version
echo.

echo 4. Testing Quarto CLI render:
"C:\Quarto\quarto_1_7_32\bin\quarto.exe" render test_quarto_extension.qmd --to html
echo.

echo 5. Opening VS Code with test file:
start "" code test_quarto_extension.qmd
echo.

echo Test completed. VS Code should now open with the Quarto file.
echo To test the extension further:
echo   1. Open Command Palette (Ctrl+Shift+P)
echo   2. Type "Quarto: Verify Installation"
echo   3. Check if it finds your Quarto installation
echo   4. Try "Quarto: Preview" to preview the file
echo.
pause
