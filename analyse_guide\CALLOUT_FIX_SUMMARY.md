# Callout Block Fix Summary

## Issue Identified

- **Root Cause:** Prettier formatter was breaking Quarto callout blocks because it's designed for GitHub-flavored Markdown, not Pandoc Markdown
- **Symptom:** Callout blocks weren't rendering properly in HTML output
- **Solution:** Add empty lines before and after content within callout blocks

## Fix Applied

Applied the VM's Numbers Station recommended solution:

### Before (broken):

```markdown
::: {.callout-tip icon="clock" collapse="false"}
**Estimated Time:** 20 minutes
:::
```

### After (fixed):

```markdown
::: {.callout-tip icon="clock" collapse="false"}

**Estimated Time:** 20 minutes

:::
```

## Files Modified

1. **`.prettierrc.json`** - Updated to use `"embeddedLanguageFormatting": "off"` for QMD files
2. **`workflow_sea_level_rise_analysis.qmd`** - Added empty lines to all 13 callout blocks

## Callout Blocks Fixed

- ✅ 13 callout blocks total
- ✅ All now have proper empty line formatting
- ✅ Should render correctly in HTML output
- ✅ Prettier-safe formatting maintained

## Result

The document should now render all callout blocks properly without Pretti<PERSON> breaking the Pandoc Markdown syntax.
