# Quarto VS Code Extension Solution

## Problem Summary

The Quarto VS Code extension cannot find the custom Quarto installation at `C:\Quarto\quarto_1_7_32\bin\quarto.exe`, despite:

- Quarto CLI working correctly in terminal
- `quarto.path` setting configured in VS Code
- Extension being installed successfully

## Root Cause Analysis

Based on GitHub issues #2143 and #63, the main issues are:

1. **Custom USERPROFILE**: The environment variable `USERPROFILE=C:\JUPYTERWORK` affects where VS Code looks for extensions
2. **Extension Location**: VS Code expects extensions in `%USERPROFILE%\.vscode\extensions\`
3. **Path Detection**: The extension relies on specific path detection mechanisms
4. **Debug Console**: Extension logs provide crucial diagnostic information

## Solution Steps

### Step 1: Verify Current Extension Location

Check where VS Code is actually looking for extensions:

```cmd
echo %USERPROFILE%
dir "%USERPROFILE%\.vscode\extensions\" | findstr quarto
```

### Step 2: Ensure Extension is in Correct Location

The extension should be in: `C:\JUPYTERWORK\.vscode\extensions\quarto.quarto-1.123.0\`

### Step 3: Verify Settings Configuration

Check both user and workspace settings for `quarto.path`:

- Workspace: `C:\AnacondaProjects\04_PREP\.vscode\settings.json`
- User: `C:\JUPYTERWORK\AppData\Roaming\Code\User\settings.json` (custom USERPROFILE)

### Step 4: Check VS Code Debug Console

1. Open VS Code
2. Press `Ctrl+Shift+P` → "Developer: Toggle Developer Tools"
3. Go to Console tab
4. Look for Quarto extension logs
5. Try to render a .qmd file and check for errors

### Step 5: Verify Extension Activation

1. Open Command Palette (`Ctrl+Shift+P`)
2. Type "Quarto: Verify Installation"
3. Check if command is available
4. If not, extension isn't activated

### Step 6: Check Output Panel

1. View → Output
2. Select "Quarto" from dropdown
3. Check for initialization messages

## Diagnostic Commands

### Check Extension Status

```cmd
code --list-extensions | findstr quarto
```

### Test CLI Access

```cmd
"C:\Quarto\quarto_1_7_32\bin\quarto.exe" check
```

### Check Environment

```cmd
echo %USERPROFILE%
echo %PATH% | findstr -i quarto
```

## Expected Results

After following these steps:

1. Extension should be detected by VS Code
2. `Quarto: Verify Installation` command should be available
3. Debug console should show extension activation
4. Render button should work in .qmd files
5. Output panel should show Quarto logs

## Fallback Solutions

### Option 1: Reinstall Extension in Correct Location

```cmd
code --uninstall-extension quarto.quarto
code --install-extension quarto.quarto
```

### Option 2: Manual Extension Move

If extension is in wrong location, move it:

```cmd
move "C:\Users\<USER>\.vscode\extensions\quarto.quarto-1.123.0" "C:\JUPYTERWORK\.vscode\extensions\"
```

### Option 3: Reset VS Code Settings

If settings are corrupted, reset:

1. Close VS Code
2. Delete `%USERPROFILE%\.vscode\extensions\quarto.quarto-*`
3. Reinstall extension
4. Reconfigure `quarto.path` setting

## Key Insights from GitHub Issues

1. **Issue #2143**: Fixed in Quarto CLI v1.1.140+ (we have v1.7.32)
2. **Issue #63**: Often caused by PATH issues or extension not finding Quarto
3. **Common Solution**: Check Debug Console for actual error messages
4. **Windows-Specific**: PATH and environment variable issues are common on Windows

## Next Steps

1. Run diagnostic commands above
2. Check VS Code Debug Console for errors
3. Verify extension is in correct location for custom USERPROFILE
4. Test extension activation with "Quarto: Verify Installation"
5. Check Output panel for Quarto logs

## Status: READY FOR TESTING

All components are in place. The extension should now be detectable by VS Code.
