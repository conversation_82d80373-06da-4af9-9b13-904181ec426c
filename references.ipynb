{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "jupyter: python3\n", "bibliography: references.bib\n", "biblatex: true\n", "# No need to specify biblatexoptions here as they are inherited from _quarto.yml\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# References {.unnumbered}\n"]}], "metadata": {"kernelspec": {"display_name": "prep_book", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}