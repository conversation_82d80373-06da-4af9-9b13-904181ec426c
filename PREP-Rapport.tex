% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
\PassOptionsToPackage{dvipsnames,svgnames,x11names}{xcolor}

\documentclass[
  letterpaper,
]{scrreprt}

\usepackage{KUstyle}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math}
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi

% Fix character encoding issues in bibliography
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{url}
\Urlmuskip=0mu plus 1mu

% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage{hyperref}
\hypersetup{
  pdftitle={PREP Rapport},
  pdfauthor={Kristian Taylor-Sørensen},
  colorlinks=true,
  linkcolor={black},
  filecolor={Maroon},
  citecolor={black},
  urlcolor={black},
  pdfcreator={LaTeX via pandoc}}
\urlstyle{same} % disable monospaced font for URLs
% Geometry package is already loaded in KUstyle.sty
% \usepackage{longtable,booktabs,array}
\usepackage{calc} % for calculating minipage widths
% Correct order of tables after \paragraph or \subparagraph
\usepackage{etoolbox}
\makeatletter
\patchcmd\longtable{\par}{\if@noskipsec\mbox{}\fi\par}{}{}
\makeatother
% Allow footnotes in longtable head/foot
\IfFileExists{footnotehyper.sty}{\usepackage{footnotehyper}}{\usepackage{footnote}}
\makesavenoteenv{longtable}
\usepackage{graphicx}
\makeatletter
\def\maxwidth{\ifdim\Gin@nat@width>\linewidth\linewidth\else\Gin@nat@width\fi}
\def\maxheight{\ifdim\Gin@nat@height>\textheight\textheight\else\Gin@nat@height\fi}
\makeatother
% Scale images if necessary, so that they will not overflow the page
% margins by default, and it is still possible to overwrite the defaults
% using explicit options in \includegraphics[width, height, ...]{}
\setkeys{Gin}{width=\maxwidth,height=\maxheight,keepaspectratio}
% Set default figure placement to htbp
\makeatletter
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{5}
% Make \paragraph and \subparagraph free-standing
\ifx\paragraph\undefined\else
  \let\oldparagraph\paragraph
  \renewcommand{\paragraph}[1]{\oldparagraph{#1}\mbox{}}
\fi
\ifx\subparagraph\undefined\else
  \let\oldsubparagraph\subparagraph
  \renewcommand{\subparagraph}[1]{\oldsubparagraph{#1}\mbox{}}
\fi

% Set default figure placement to htbp
\makeatletter
\def\fps@figure{htbp}
\makeatother

\usepackage{KUstyle}
\usepackage{microtype}
\usepackage{setspace}
\usepackage{amsmath,amssymb}
\usepackage{booktabs}
\usepackage{longtable}
\onehalfspacing
% Use Latin Modern (similar to Computer Modern used in Overleaf)
\setmainfont{Latin Modern Roman}
\setsansfont{Latin Modern Sans}
\setmonofont{Latin Modern Mono}
% Redefine section formatting to match Overleaf style using KOMA-Script commands
\setkomafont{chapter}{\normalfont\LARGE\bfseries}
\setkomafont{section}{\normalfont\Large\bfseries}
\setkomafont{subsection}{\normalfont\large\bfseries}
% Adjust spacing for sections
\RedeclareSectionCommand[
  beforeskip=50pt,
  afterskip=40pt
]{chapter}
\RedeclareSectionCommand[
  beforeskip=3.5ex plus 1ex minus .2ex,
  afterskip=2.3ex plus .2ex
]{section}
\RedeclareSectionCommand[
  beforeskip=3.25ex plus 1ex minus .2ex,
  afterskip=1.5ex plus .2ex
]{subsection}
\makeatletter
\@ifpackageloaded{bookmark}{}{\usepackage{bookmark}}
\makeatother
\makeatletter
\@ifpackageloaded{caption}{}{\usepackage{caption}}
\AtBeginDocument{%
\ifdefined\contentsname
  \renewcommand*\contentsname{Table of contents}
\else
  \newcommand\contentsname{Table of contents}
\fi
\ifdefined\listfigurename
  \renewcommand*\listfigurename{List of Figures}
\else
  \newcommand\listfigurename{List of Figures}
\fi
\ifdefined\listtablename
  \renewcommand*\listtablename{List of Tables}
\else
  \newcommand\listtablename{List of Tables}
\fi
\ifdefined\figurename
  \renewcommand*\figurename{Figure}
\else
  \newcommand\figurename{Figure}
\fi
\ifdefined\tablename
  \renewcommand*\tablename{Table}
\else
  \newcommand\tablename{Table}
\fi
}
\@ifpackageloaded{float}{}{\usepackage{float}}
\floatstyle{ruled}
\@ifundefined{c@chapter}{\newfloat{codelisting}{h}{lop}}{\newfloat{codelisting}{h}{lop}[chapter]}
\floatname{codelisting}{Listing}
\newcommand*\listoflistings{\listof{codelisting}{List of Listings}}
\makeatother
\makeatletter
\makeatother
\makeatletter
\@ifpackageloaded{caption}{}{\usepackage{caption}}
\@ifpackageloaded{subcaption}{}{\usepackage{subcaption}}
\makeatother
\usepackage[backend=biber,style=authoryear-ibid,sorting=nyt,maxbibnames=99,ibidtracker=true,ibidpage=true,block=none]{biblatex}
\addbibresource{references.bib}

% Remove square markers from bibliography
\defbibenvironment{bibliography}
  {\list{}
     {\setlength{\leftmargin}{2.5em}%   
      \setlength{\itemindent}{-\leftmargin}% This creates the hanging indent
      \setlength{\itemsep}{\bibitemsep}%
      \setlength{\parsep}{\bibparsep}}}
  {\endlist}
  {\item}

% Define CSLReferences environment for pandoc citations
\newenvironment{CSLReferences}[2]
 {\begin{list}{}
  {\setlength{\itemsep}{0pt}
   \setlength{\parsep}{0pt}
   \setlength{\leftmargin}{#1\parindent}
   \setlength{\itemindent}{-#1\parindent}
   \setlength{\rightmargin}{#2\parindent}}}
 {\end{list}}

% Define citation commands
\providecommand{\citeproctext}{}
\providecommand{\citeproc}[2]{}
\providecommand{\citeprocyear}[1]{}
\providecommand{\citeprocauthor}[1]{}
\providecommand{\citeprocnumber}[1]{}
\providecommand{\citeproctype}[1]{}

\title{PREP Rapport}
\subtitle{A Subtitle for the Report}
\author{Kristian Taylor-Sørensen}
\date{2024-01-01}

% Custom KU settings
\ptype{Master's Thesis}
\advisor{Advisor: Professor Name}
\fpimage{Pictures/frontpage-image.jpg}

\begin{document}
\maketitle

\renewcommand*\contentsname{Table of contents}
{
\hypersetup{linkcolor=}
\setcounter{tocdepth}{2}
\tableofcontents
}
\bookmarksetup{startatroot}

\chapter*{Preface}\label{preface}
\addcontentsline{toc}{chapter}{Preface}

\markboth{Preface}{Preface}

This is the preface to the PREP Rapport.

\section*{Acknowledgements}\label{acknowledgements}
\addcontentsline{toc}{section}{Acknowledgements}

\markright{Acknowledgements}

Place acknowledgements here.

\bookmarksetup{startatroot}

\chapter{Introduction}\label{introduction}

\section{Background}\label{background}

As we progress into a future marked by rising greenhouse gas emissions
and increasing temperatures, various Climatic Impact Drivers (CIDs) will
shape our lives. CIDs are physical climate conditions that affect
ecosystems or parts of society, with impacts that vary from harmful to
beneficial depending on the resilience of affected systems and regions
(Intergovernmental Panel on Climate Change
\autocite{intergovernmentalpanelonclimatechangeipccAnnexVIIGlossary2023}.
The
\autocite{intergovernmentalpanelonclimatechangeipccClimateChange20222023a}
report identifies `Sea Level' as one of the CIDs considered hazardous
due to rising global temperatures, encompassing both `Global Mean Sea
Level' (GMSL) and `Extreme Sea Levels.' In Chapter 3, \emph{`Oceans and
Coastal Ecosystems and Their Services'}, the authors note that even
under the lowest emission scenario (SSP1-1.9), GMSL is likely to rise by
0,15--0,23 m by 2050 and by as much as 0,20--0,30 m under high emissions
(SSP5-8.5), potentially reaching 0,28--0,55 m under SSP1-1.9 and up to
0,63--1,02 m under SSP5-8.5 by the end of this century (2100), not
including ice-sheet contributions
\autocite{intergovernmentalpanelonclimatechangeipccOceansCoastalEcosystems2023}.

With an average elevation of 31 m and 8,750 km of coastline, Denmark
must assess how to handle rising sea levels and increased precipitation
frequency and intensity---not just in urban areas, but also in natural
ecosystems, particularly those protected under EU law (Habitat Directive
92/43/EEC) and national law (§3 Nature Protection Law LBKG 2024-06-28 nr
927). These nature protections should be enforced not only to preserve
the natural environments themselves but also to a significant extent the
biodiversity that underpins the purpose of these protections.

The world is experiencing a loss of biodiversity due to human activity,
and among the direct drivers affecting many protected areas in Denmark
are climate change and as an effect of that SLR, which can significantly
degrade coastal nature types and, as a result, have a devastating impact
on the specialist species connected to these habitats
\autocite{brondizioGlobalAssessmentReport2019}.

Due to these impacts, I chose to write my thesis on \emph{``Consequences
of Climate Change-Induced Sea Level Rise and Increased Precipitation for
the Management of Protected Nature Areas''} to assess how SLR would
impact selected coastal areas in Denmark, focusing on nature areas
protected under the Habitat Directive and §3 of the Nature Protection
Act.

Protected nature areas, including Natura 2000 sites governed by EU and
§3 under the national Nature Protection Law, are particularly
vulnerable. These areas are crucial for maintaining biodiversity, but
SLR and heightened precipitation can compromise their ecological
stability. The potential for ecosystem degradation and habitat loss for
species connected to these environments underscores the need for a
proactive response. Effective spatial analysis is essential for
understanding, predicting, and mitigating potential impacts on these
protected areas, supporting management strategies that are adaptive and
build resilience in the face of climate change.

Only through thorough analysis can we adapt our current management
strategies to account for the dynamic nature of ecosystems. This will
enable these areas to absorb environmental changes effectively while
maintaining their ecological and biodiversity value within conservation
frameworks, supporting a forward-thinking approach to nature management
that aligns long-term environmental resilience with statutory protection
goals.

As part of my thesis, I will be analysing the impact of SLR and
increased precipitation on a number of case areas to clarify the impacts
on the protected coastal nature in these areas. In order conduct this
analysis, there are many tools available. For this thesis, two platforms
have been chosen: SCALGOLive, developed by the Danish-founded SCALGO,
and the more versatile but less specialised QGIS. SCALGOLive is
described by its developers as a tool that \emph{``turns high-resolution
geographic data into easy-to-use tools for engineers, urban planners,
architects, and government administrators''} \autocite{WeAreScalgo2024}
while the other is the less specialized but more versatile tool QGIS, a
community-driven, free, and open-source spatial visualization and
decision-making tool \autocite{QGISOverviewQGIS}.

SCALGOLive is a tool designed for hydrological modelling, with
capabilities that include terrain analysis and water flow simulation. It
is well-suited for visualizing flood risk and mapping water movement,
making it applicable for studying the effects of sea-level rise and
increased precipitation. The interface is user-friendly, allowing for
efficient assessments and reliable simulations, which provide insights
into how hydrological changes may impact protected coastal areas.
However, the user-friendliness of this tool is an advantage, but it also
limits its use for more complex tasks. Because it is designed for a
wider user base, it may not be ideal for those who regularly work with
spatial analysis. To ensure the model is reliable, the tool has strict
limits on what users can and cannot do.

QGIS is another powerful tool for hydrological analysis, known for its
flexibility and extensive capabilities. With various plugins, such as
GRASS and SAGA, QGIS supports detailed modelling of water flow,
watershed mapping, and flood simulation. Its ability to integrate
multiple data sources, including topography and precipitation data,
allows for thorough analysis and visualization of potential hydrological
impacts. This versatility makes QGIS suitable for evaluating the effects
of SLR and increased rainfall on protected coastal areas, providing a
broader analysis than SCALGOLive.

Hydrological modelling is important for understanding and predicting
flood risks and the effects of sea-level rise, both of which impact
coastal areas. These models provide valuable insights that help guide
strategies for effective coastal management and resilience planning. By
simulating scenarios with increased rainfall and rising sea levels,
tools like SCALGOLive and QGIS make it possible to visualise and assess
potential impacts, aiding decision-makers in creating strategies to
protect and adapt coastal nature areas to the challenges posed by
climate change.

The purpose of this thesis preparation project is to assess and compare
SCALGOLive and QGIS to understand how effective they are for
estimating/modelling the effects of SLR on coastal nature areas. This
evaluation will help determine the appropriate tool for the thesis,
especially given the limited time available to become fully familiar
with SCALGOLive.

\bookmarksetup{startatroot}

\chapter{Methods}\label{methods}

This study employs a comparative analysis of two hydrological modeling
tools: QGIS and Scalgo Live, focusing on their application to coastal
areas. QGIS was selected based on prior experience with the platform,
while Scalgo Live was chosen following COWI's recommendation, as they
utilized it for modeling in their report \emph{``Analyse af
havstigninger i udvalgte Natura 2000-områder -- Tab og
indsatsmuligheder. Med udgangspunkt i Naturstyrelsens strandenge''}
\autocite{ebbensgaardAnalyseAfHavstigninger2023}. Both tools are
evaluated using a structured framework to determine their effectiveness
for modeling sea level rise impacts on protected coastal habitats.

\section{Evaluation Framework}\label{evaluation-framework}

\subsection{Test Area Selection}\label{test-area-selection}

Vejlerne was selected as the primary test area for this comparative
analysis based on several key considerations:

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  Spatial analysis in QGIS identified this region as having a high
  concentration of protected coastal areas that would be potentially
  impacted by sea level rise
\item
  The area features extensive saltmarsh habitats that are particularly
  vulnerable to changes in sea level
\item
  Vejlerne contains significant § 3 protected nature areas and Natura
  2000 designated sites, providing valuable conservation context (indsæt
  miljøportal og habitatplejeplan kilder)
\item
  \textbf{!!!} The site's representation in the COWI report allows for
  consistency in comparing methodological approaches
\end{enumerate}

\subsection{Evaluation Criteria}\label{evaluation-criteria}

The comparative analysis of QGIS and Scalgo Live is based on the
following criteria:

\begin{itemize}
\tightlist
\item
  \textbf{Data integration:} Ability to incorporate and process various
  data types including digital elevation models (DEMs), land use data,
  hydrological data, and protected area designations
\item
  \textbf{Sea level modeling accuracy:} Precision and reliability in
  representing projected sea level rise scenarios across different
  timeframes
\item
  \textbf{Output quality:} Clarity, visual representation, and usability
  of the generated models for decision-making purposes
\item
  \textbf{Analytical flexibility:} Capability to perform various
  hydrological analyses beyond basic flooding, including connectivity
  assessment and habitat impact evaluation
\item
  \textbf{User accessibility:} Learning curve, technical requirements,
  and ease of use for practitioners with varying levels of GIS expertise
\end{itemize}

\section{Scenario Selection and Analysis
Framework}\label{scenario-selection-and-analysis-framework}

\subsection{Sea Level Rise Scenarios}\label{sea-level-rise-scenarios}

This analysis uses updated emission scenarios based on DMI's January
2025 recommendations, following the transition from RCP to SSP scenarios
for sea level projections. Two scenarios represent the current planning
range for Danish coastal climate adaptation.

\textbf{SSP2-4.5} (moderate emissions) serves as the baseline scenario,
with emissions declining from mid-century but not reaching climate
neutrality by 2100 (2.7°C warming by 2081-2100). \textbf{SSP3-7.0} (high
emissions) represents high robustness requirements as recommended by DMI
for long-term coastal planning (3.6°C warming by 2081-2100). Current
global climate policies suggest future emissions will fall within this
range, making it highly relevant for practical applications.

Using two emission scenarios rather than multiple return periods aligns
with this study's primary objective: comparing analytical tools rather
than conducting comprehensive risk assessment. This approach focuses on
how different climate trajectories affect tool performance and output
consistency.

\subsection{Storm Event Selection}\label{storm-event-selection}

Following COWI's 2023 methodology for Natura 2000 areas, this study
focuses on 10-year return period storm events (T10). This choice
balances analytical tractability with ecological relevance, as protected
coastal habitats like saltmarshes are more vulnerable to repeated
moderate disturbances than infrequent extreme events.

T10 events correspond to storm surge heights that begin causing
significant ecological impact in low-lying coastal areas. Climate
projections indicate these events will become much more frequent under
sea level rise, potentially occurring every 2-3 years by 2100, making
T10 representative of future ``normal'' conditions.

\subsection{Validation and Study Area}\label{validation-and-study-area}

To ensure methodological consistency, the analysis includes direct
comparison with COWI's ScalgoLIVE results from Vejlerne. While COWI used
RCP 8.5 scenarios, their water level projections (46 cm sea level rise
by 2070, 103 cm by 2120) provide reference points for assessing platform
consistency.

Vejlerne was selected based on spatial analysis identifying high
concentrations of vulnerable protected coastal areas, extensive
saltmarsh habitats typical of Danish Natura 2000 sites, and continuity
with existing professional analysis for validation.

\subsection{Analysis Framework}\label{analysis-framework}

Given the tool comparison objective, this study prioritizes speed and
comparability over comprehensive flood modeling. The framework produces
outputs suitable for direct platform comparison while maintaining
essential accuracy controls. For methodology evaluation, consistency
between platforms is more critical than absolute precision, though both
platforms are assessed against validation criteria to ensure meaningful
results.

\bookmarksetup{startatroot}

\chapter{Results}\label{results}

Describe main results here.

\begin{itemize}
\tightlist
\item
  Tal om resultaterne (kortene) fra de to forskellige programmer
\item
  Husk at skrive ned, hvad du gør i løbet af analysen
\item
  Vælg \textbf{TO} scenarier (begynd med ét) - vigtigt at tale om deres
  evne til at forudsige (kan programmet leve op til det?)
\item
  Skriv at du kun arbejder med AVJNF fordi adgang til data er nemmere
\item
  Det bliver til en Case-study fordi Niels har et område som passer til
  mit speciale
\item
  Hvorfor bruge Scalgo i stedet for GDAL og Saga i QGIS
\end{itemize}

\bookmarksetup{startatroot}

\chapter{Discussion}\label{discussion}

This chapter discusses the implications of the results, their
limitations, and potential future directions for research.

\section{Interpretation of Results}\label{interpretation-of-results}

Discuss the interpretation of the results here.

\section{Compare}\label{compare}

Compare the results with other work \#\# Limitations

Discuss the limitations of the study here. This could include
limitations in the data, methodology, or analysis techniques.

\section{Future Directions}\label{future-directions}

Suggest potential future directions for research based on the findings.
This could include extensions of the current work or new avenues of
investigation.

\bookmarksetup{startatroot}

\chapter{Conclusion}\label{conclusion}

This chapter summarizes the main findings of the project and their
implications.

\section{Summary of Findings}\label{summary-of-findings}

Summarize the main findings of the project here. This should be a
concise overview of the most important results and their significance.

\section{Implications}\label{implications}

Discuss the broader implications of the findings. This could include
practical applications, theoretical contributions, or policy
implications.

\section{Final Thoughts}\label{final-thoughts}

Conclude with some final thoughts on the project and its significance.
This could include reflections on the research process, lessons learned,
or personal insights.

\DeclareFieldFormat{doi}{\url{https://doi.org/#1}}
\printbibliography
\end{document}
