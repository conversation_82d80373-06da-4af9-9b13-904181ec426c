# Quarto Callout Structure Fix Guide

## Problem

Callout closing `:::` markers were automatically moving up during rendering, causing callouts to fail. This is typically
caused by markdown linting rules that auto-format documents.

## Solution Applied

Fixed all callout structures to follow proper Quarto syntax:

### ✅ Correct Callout Structure:

```markdown
::: {.callout-note icon="info-circle"} Content goes here on separate lines. Multiple lines are supported. :::
```

### ❌ Incorrect Structure (causes failures):

```markdown
::: {.callout-note} Content on same line :::
```

## Prevention Strategy

### 1. VS Code Settings

Add to your VS Code `settings.json`:

```json
{
  "markdownlint.config": {
    "MD033": false,
    "MD013": false
  },
  "[markdown]": {
    "editor.defaultFormatter": null
  }
}
```

### 2. Markdown Lint Configuration

Create `.markdownlint.json` in project root:

```json
{
  "MD033": false,
  "MD013": false,
  "MD041": false,
  "MD024": {
    "siblings_only": true
  }
}
```

### 3. Prettier Configuration for QMD Files

Create `.prettierrc.json` in project root:

```json
{
  "printWidth": 120,
  "proseWrap": "preserve",
  "singleQuote": true,
  "trailingComma": "all",
  "arrowParens": "avoid",
  "useTabs": false,
  "tabWidth": 2,
  "overrides": [
    {
      "files": ["*.qmd", "*.md"],
      "options": {
        "proseWrap": "preserve",
        "printWidth": 120,
        "tabWidth": 2,
        "useTabs": false,
        "embeddedLanguageFormatting": "off"
      }
    }
  ]
}
```

### 4. Enhanced VS Code Settings

Update your `settings.json` with QMD-specific configuration:

```json
{
  "[quarto]": {
    "editor.wordWrap": "bounded",
    "editor.wordWrapColumn": 120,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.snippetSuggestions": "inline",
    "editor.rulers": [],
    "editor.quickSuggestions": {
      "comments": "off",
      "strings": "off",
      "other": "off"
    }
  },
  "prettier.proseWrap": "preserve",
  "prettier.embeddedLanguageFormatting": "off",
  "markdownlint.config": {
    "MD033": false,
    "MD013": false,
    "MD041": false
  }
}
```

### 3. Quarto-Specific Rules

- Always place opening `:::` on its own line
- Always place closing `:::` on its own line
- Leave blank lines before and after callout blocks
- Use proper callout syntax with attributes in braces

## Fixed Callouts in Document

- ✅ All callout-tip blocks with time estimates
- ✅ All callout-note blocks with icons
- ✅ All callout-warning blocks
- ✅ All callout-important blocks
- ✅ Panel tabset closing markers
- ✅ Column block closing markers

## Verification

Document now renders correctly with all callouts functioning as intended.
