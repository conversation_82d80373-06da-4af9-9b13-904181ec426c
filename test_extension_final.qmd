---
title: "Quarto Extension Test"
format: html
jupyter: python3
---

# Test Document

This is a test document to verify the Quarto VS Code extension is working correctly.

## Python Code Test

```{python}
print("Hello from Quarto!")
print(f"2 + 2 = {2 + 2}")
```

## Information

- File location: `C:\AnacondaProjects\04_PREP\test_extension_final.qmd`
- Quarto path: `C:\Quarto\quarto_1_7_32\bin\quarto.exe`
- Extension location: `C:\JUPYTERWORK\.vscode\extensions\quarto.quarto-1.123.0`
- Test time: 2025-01-08

## Expected Behavior

When you open this file in VS Code:

1. Quarto extension should be active
2. "Render" button should be visible in the toolbar
3. Command palette should show "Quarto: Verify Installation"
4. Extension should detect Quarto at the configured path
5. Rendering should work without errors

## Troubleshooting Steps

If the extension doesn't work:

1. Check VS Code Output panel → "Quarto"
2. Open Developer Tools (Ctrl+Shift+P → "Developer: Toggle Developer Tools")
3. Check Console for errors
4. Verify extension is in correct location: `C:\JUPYTERWORK\.vscode\extensions\quarto.quarto-1.123.0`
5. Restart VS Code completely
