## Debug Steps for Quarto Extension Issue

Based on GitHub issue #2143, here are the debugging steps:

### 1. **Check Debug Console Output**

- Open VS Code
- Go to `View` → `Output`
- Select "Quarto" from the dropdown
- Try to run a Quarto command and check for error messages

### 2. **Verify Terminal Environment**

The issue mentions problems with PowerShell vs Git Bash. Let's test:

```cmd
# Test in Command Prompt
quarto --version

# Test in PowerShell
powershell -c "quarto --version"
```

### 3. **Extension Path Configuration**

Make sure the `quarto.path` setting is correctly formatted:

- Should be: `C:\\Quarto\\quarto_1_7_32\\bin\\quarto.exe`
- NOT the bin directory, but the actual executable

### 4. **Reload VS Code Extension Host**

- Press `Ctrl+Shift+P`
- Type "Developer: Reload Window"
- This forces VS Code to reload all extensions

### 5. **Check Extension Activation**

The extension should activate when:

- Opening a .qmd file
- Having \_quarto.yml in workspace
- Using Quarto commands

Let's test these steps...
