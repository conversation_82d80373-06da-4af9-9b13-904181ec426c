# Quarto VS Code Extension Troubleshooting Guide

## Quick Fix (Most Common)

1. **Open Command Palette** (`Ctrl+Shift+P`)
2. **Type**: `Python: Select Interpreter`
3. **Select**: Your conda environment (`prep_book`)
4. **Restart VS Code**

## Advanced Troubleshooting

### Option A: Environment Variable Fix

If the Python interpreter selection doesn't work, set the QUARTO_PYTHON environment variable:

1. **Open PowerShell as Administrator**
2. **Run**: `setx QUARTO_PYTHON "C:\AnacondaPath\envs\prep_book\python.exe"`
3. **Restart VS Code**

### Option B: Update Workspace Settings

Add this to your workspace settings (`.vscode/settings.json`):

```json
{
  "python.defaultInterpreterPath": "C:\\AnacondaPath\\envs\\prep_book\\python.exe",
  "quarto.path": "C:\\Quarto\\quarto_1_7_32\\bin\\quarto.exe"
}
```

### Option C: Extension Reset

1. **Disable** the Quarto extension
2. **Restart VS Code**
3. **Enable** the Quarto extension
4. **Restart VS Code** again

### Option D: Check Dependencies

Run in terminal:

```bash
pip install jupyter nbformat nbclient
```

## Verification

After trying any fix:

1. Open the test file: `test_quarto_commands.qmd`
2. Press `Ctrl+Shift+P`
3. Search for "Quarto"
4. Commands should appear without "not found" errors

## Common Issues

- **Wrong Python interpreter**: Make sure you're using the conda environment, not system Python
- **Extension not activated**: The Quarto extension needs the Python extension to work
- **Missing Jupyter**: Ensure Jupyter is installed in your selected Python environment
